import AsyncStorage from '@react-native-async-storage/async-storage';
import { decryptFromStorage, encryptForStorage, decryptApiData } from './contentService';
import { API_URL } from './api';
import * as Sentry from '@sentry/react-native';

// Types
export interface PenaltyItem {
  id: string;
  text_en: string;
  text_es: string;
  text_dom: string;
  isPremium?: boolean;
  isDefaultFree?: boolean;
  isDefaultPremium?: boolean;
}

export interface PenaltyContent {
  drinking: PenaltyItem[];
  physical: PenaltyItem[];
  social: PenaltyItem[];
  silly: PenaltyItem[];
  creative: PenaltyItem[];
}

export type PenaltyCategory = keyof PenaltyContent;

// New type for individual penalty selections
export type PenaltySelection = string; // penalty ID

// Storage keys
const PENALTY_CONTENT_STORAGE_KEY = 'penalty_content';
const PENALTY_LAST_FETCH_KEY = 'penalty_last_fetch';
const PENALTY_SELECTIONS_KEY = 'penalty_selections';
const PENALTY_INDIVIDUAL_SELECTIONS_KEY = 'penalty_individual_selections';

// Cache duration (10 seconds)
// const CACHE_DURATION = 10 * 1000; // 10 seconds in milliseconds
const CACHE_DURATION = 60 * 60 * 1000; // Productions

// Note: Removed internet connectivity check - we'll try to fetch directly and fall back to cache if it fails

// Fetch penalties from API
const fetchPenaltiesFromAPI = async (): Promise<PenaltyContent | null> => {
  try {
    const penaltyUrl = `${API_URL}/penalties`;
    console.log('Fetching penalties from API:', penaltyUrl);
    const response = await fetch(penaltyUrl, {
      method: 'GET',
      // Content-Type header omitted to let the client set it automatically
      // This fixes Android-specific network issues
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Penalties API response received');

    // Handle encrypted response (similar to content service)
    if (data.encrypted && data.data) {
      console.log('Decrypting penalty data from API...');
      try {
        const decryptedData = decryptApiData(data.data);
        console.log('Successfully decrypted penalty data from API');
        return decryptedData;
      } catch (decryptError) {
        console.error('Failed to decrypt penalty data from API:', decryptError);
        Sentry.captureException(decryptError);
        return null;
      }
    }

    // Response is not encrypted
    console.log('Received unencrypted penalty data from API');
    return data;
  } catch (error) {
    console.error('Error fetching penalties from API:', error);
    Sentry.captureException(error);
    return null;
  }
};

// Save penalties to storage
const savePenaltiesToStorage = async (penalties: PenaltyContent): Promise<boolean> => {
  try {
    console.log('Preparing penalty content for secure storage...');

    // Prepare the content for storage (Base64 encoding)
    const preparedContent = encryptForStorage(penalties);

    console.log('Penalty content successfully prepared. Length:', preparedContent.length);

    // Save to AsyncStorage
    await AsyncStorage.setItem(PENALTY_CONTENT_STORAGE_KEY, preparedContent);
    await AsyncStorage.setItem(PENALTY_LAST_FETCH_KEY, Date.now().toString());
    console.log('Penalty content saved to AsyncStorage');

    return true;
  } catch (error) {
    console.error('Error preparing or saving penalty content to storage:', error);
    return false;
  }
};

// Get cached penalties from storage
const getCachedPenalties = async (): Promise<PenaltyContent | null> => {
  try {
    const cachedContent = await AsyncStorage.getItem(PENALTY_CONTENT_STORAGE_KEY);

    if (!cachedContent) {
      console.log('No cached penalty content found');
      return null;
    }

    console.log('Decrypting cached penalty content...');
    const decryptedContent = decryptFromStorage(cachedContent);

    // decryptFromStorage already returns parsed JSON, no need to parse again
    if (typeof decryptedContent === 'string') {
      const penalties = JSON.parse(decryptedContent);
      console.log('Cached penalty content loaded successfully');
      return penalties;
    } else {
      console.log('Cached penalty content loaded successfully');
      return decryptedContent;
    }
  } catch (error) {
    console.error('Error loading cached penalty content:', error);
    return null;
  }
};

// Check if cache is expired
const isCacheExpired = async (): Promise<boolean> => {
  try {
    const lastFetchStr = await AsyncStorage.getItem(PENALTY_LAST_FETCH_KEY);
    if (!lastFetchStr) {
      return true;
    }

    const lastFetch = parseInt(lastFetchStr, 10);
    const now = Date.now();
    const isExpired = (now - lastFetch) > CACHE_DURATION;

    console.log(`Penalty cache age: ${Math.round((now - lastFetch) / 1000 / 60)} minutes, expired: ${isExpired}`);
    return isExpired;
  } catch (error) {
    console.error('Error checking penalty cache expiration:', error);
    return true;
  }
};

// Result type to distinguish between API success and cache fallback
export interface PenaltyFetchResult {
  content: PenaltyContent | null;
  fromCache: boolean;
}

// Main function to fetch and update penalty content (similar to fetchAndUpdateGameContent)
export const fetchAndUpdatePenalties = async (forceRefresh: boolean = false): Promise<PenaltyFetchResult> => {
  try {
    console.log('🎯 Starting penalty content fetch process...');

    // Check if we've refreshed penalties recently (within the last hour)
    if (!forceRefresh) {
      const cacheExpired = await isCacheExpired();

      if (!cacheExpired) {
        console.log('⏰ Penalty cache is still valid, using cached content');
        const cachedContent = await getCachedPenalties();
        if (cachedContent) {
          return { content: cachedContent, fromCache: true };
        }
      }
    }

    // Try to fetch fresh content from API (similar to content service approach)
    const freshPenalties = await fetchPenaltiesFromAPI();

    if (freshPenalties) {
      console.log('✅ Fresh penalty content fetched successfully');

      // Save to cache
      await savePenaltiesToStorage(freshPenalties);

      return { content: freshPenalties, fromCache: false };
    } else {
      console.log('❌ Failed to fetch fresh penalty content, falling back to cache');
      const cachedContent = await getCachedPenalties();
      return { content: cachedContent, fromCache: true };
    }
  } catch (error) {
    console.error('Error in fetchAndUpdatePenalties:', error);
    Sentry.captureException(error);
    const cachedContent = await getCachedPenalties();
    return { content: cachedContent, fromCache: true };
  }
};

// Load penalty content from cache only (similar to getCachedGameContent)
export const loadPenaltyContent = async (): Promise<PenaltyContent | null> => {
  try {
    console.log('Loading penalty content from cache only');
    return await getCachedPenalties();
  } catch (error) {
    console.error('Error loading penalty content from cache:', error);
    Sentry.captureException(error);
    return null;
  }
};

// Save user penalty selections
export const savePenaltySelections = async (selections: PenaltyCategory[]): Promise<void> => {
  try {
    await AsyncStorage.setItem(PENALTY_SELECTIONS_KEY, JSON.stringify(selections));
    console.log('Penalty selections saved:', selections);
  } catch (error) {
    console.error('Error saving penalty selections:', error);
    Sentry.captureException(error);
  }
};

// Load user penalty selections
export const loadPenaltySelections = async (): Promise<PenaltyCategory[]> => {
  try {
    const selectionsStr = await AsyncStorage.getItem(PENALTY_SELECTIONS_KEY);
    if (selectionsStr) {
      const selections = JSON.parse(selectionsStr);
      console.log('Penalty selections loaded:', selections);
      return selections;
    }
    return [];
  } catch (error) {
    console.error('Error loading penalty selections:', error);
    return [];
  }
};

// Save individual penalty selections (new function for individual penalty IDs)
export const saveIndividualPenaltySelections = async (selections: PenaltySelection[]): Promise<void> => {
  try {
    await AsyncStorage.setItem(PENALTY_INDIVIDUAL_SELECTIONS_KEY, JSON.stringify(selections));
    console.log('Individual penalty selections saved:', selections);
  } catch (error) {
    console.error('Error saving individual penalty selections:', error);
  }
};

// Load individual penalty selections (new function for individual penalty IDs)
export const loadIndividualPenaltySelections = async (): Promise<PenaltySelection[]> => {
  try {
    const selectionsStr = await AsyncStorage.getItem(PENALTY_INDIVIDUAL_SELECTIONS_KEY);
    if (selectionsStr) {
      const selections = JSON.parse(selectionsStr);
      console.log('Individual penalty selections loaded:', selections);
      return selections;
    }
    return [];
  } catch (error) {
    console.error('Error loading individual penalty selections:', error);
    return [];
  }
};

// Get random penalty based on user selections (updated to work with individual selections)
export const getRandomPenalty = async (language: 'en' | 'es' | 'dom' = 'en'): Promise<string | null> => {
  try {
    const penalties = await loadPenaltyContent();
    const individualSelections = await loadIndividualPenaltySelections();

    if (!penalties || individualSelections.length === 0) {
      console.log('No penalties available or no individual penalties selected');
      return null;
    }

    // Collect all penalties that match the selected IDs
    const availablePenalties: PenaltyItem[] = [];
    Object.values(penalties).forEach((categoryPenalties: PenaltyItem[]) => {
      categoryPenalties.forEach((penalty: PenaltyItem) => {
        if (individualSelections.includes(penalty.id)) {
          availablePenalties.push(penalty);
        }
      });
    });

    if (availablePenalties.length === 0) {
      console.log('No penalties found matching selected IDs');
      return null;
    }

    // Select random penalty
    const randomIndex = Math.floor(Math.random() * availablePenalties.length);
    const selectedPenalty = availablePenalties[randomIndex];

    // Return text in requested language
    const textKey = `text_${language}` as 'text_en' | 'text_es' | 'text_dom';
    return selectedPenalty[textKey] || selectedPenalty.text_en;
  } catch (error) {
    console.error('Error getting random penalty:', error);
    return null;
  }
};

/**
 * Get default penalty IDs based on user's premium status
 * @param isPremiumUser - Whether the user has premium access
 * @returns Array of default penalty IDs
 */
export const getDefaultPenaltyIds = async (isPremiumUser: boolean): Promise<string[]> => {
  try {
    const penaltyContent = await loadPenaltyContent();
    if (!penaltyContent) return [];

    const defaultPenalties: string[] = [];

    // Search through all categories for default penalties
    Object.values(penaltyContent).forEach((categoryPenalties: PenaltyItem[]) => {
      categoryPenalties.forEach((penalty: PenaltyItem) => {
        if (isPremiumUser && penalty.isDefaultPremium) {
          defaultPenalties.push(penalty.id);
        } else if (!isPremiumUser && penalty.isDefaultFree) {
          defaultPenalties.push(penalty.id);
        }
      });
    });

    console.log(`🎯 Found default penalties for ${isPremiumUser ? 'premium' : 'free'} user:`, defaultPenalties);
    return defaultPenalties;
  } catch (error) {
    console.error('Error getting default penalty IDs:', error);
    return [];
  }
};

/**
 * Filter penalties based on user's premium status
 * @param penalties - Array of penalty items
 * @param isPremiumUser - Whether the user has premium access
 * @returns Filtered array of penalties accessible to the user
 */
export const filterPenaltiesByPremiumStatus = (penalties: PenaltyItem[], isPremiumUser: boolean): PenaltyItem[] => {
  if (isPremiumUser) {
    // Premium users can access all penalties
    return penalties;
  } else {
    // Free users can only access non-premium penalties
    return penalties.filter(penalty => !penalty.isPremium);
  }
};

/**
 * Check if a penalty is premium-only
 * @param penaltyId - The penalty ID to check
 * @returns True if the penalty is premium-only, false otherwise
 */
export const isPenaltyPremium = async (penaltyId: string): Promise<boolean> => {
  try {
    const penaltyContent = await loadPenaltyContent();
    if (!penaltyContent) return false;

    // Search through all categories for the penalty
    for (const categoryPenalties of Object.values(penaltyContent)) {
      const penalty = (categoryPenalties as PenaltyItem[]).find((p: PenaltyItem) => p.id === penaltyId);
      if (penalty) {
        return penalty.isPremium || false;
      }
    }

    return false;
  } catch (error) {
    console.error('Error checking if penalty is premium:', error);
    return false;
  }
};