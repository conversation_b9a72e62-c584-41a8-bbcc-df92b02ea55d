# HomeScreen Loading State Management

## Overview

The HomeScreen now implements a comprehensive loading state management system using the ToastBanner component to provide transparent feedback during content loading operations.

## Features Implemented

### 1. Comprehensive API Call Coordination
- **Three API calls executed concurrently:**
  1. Content fetching (`fetchAndUpdateGameContent`)
  2. Penalties fetching (`fetchAndUpdatePenalties`) 
  3. What's new check (`checkForNewLogs`)

### 2. Loading State Management
- **Loading State**: Shows ToastBanner with `state='loading'` during API calls
- **Success State**: Shows ToastBanner with `state='success'` when all calls succeed
- **Error State**: Shows ToastBanner with `state='error'` when any call fails

### 3. UI Interaction Control
- **During Loading**: UI opacity reduced to 30%, interactions disabled
- **Success**: Full opacity restored, interactions enabled
- **Error with cached content**: Allows dismissal by tapping UI elements or auto-dismisses after 10 seconds
- **Error without cached content**: Keeps error state until user retries

### 4. Auto-Dismissal Logic
- **Success banner**: Auto-dismisses after 3 seconds
- **<PERSON><PERSON>r banner (with cached content)**: Auto-dismisses after 10 seconds
- **Error banner (no cached content)**: Requires manual retry

### 5. Cached Content Detection
- Checks for **meaningful** cached content (not just empty structures)
- Validates that arrays contain actual items (length > 0)
- Distinguishes between empty content structures and no content at all
- Different error handling based on cache availability
- Graceful fallback for users with existing content

#### Empty Content Handling
- Empty content structures (created by `createEmptyGameContent()`) are **NOT** considered as "cached content available"
- Only content with actual questions, dares, or penalties counts as meaningful cache
- This prevents false positives where users have empty structures but no actual content

## Technical Implementation

### State Variables
```typescript
const [toastState, setToastState] = useState<'loading' | 'success' | 'error' | null>(null);
const [hasCachedContent, setHasCachedContent] = useState(false);
const [uiOpacity, setUiOpacity] = useState(1);
const [isInteractionDisabled, setIsInteractionDisabled] = useState(false);
```

### Key Functions
- `performComprehensiveApiCalls()`: Coordinates all API calls
- `checkCachedContentAvailability()`: Determines cache status
- `handleToastRetry()`: Handles retry button press
- `handleUiElementTap()`: Handles error dismissal for cached content users

### Throttling Consistency
- All services use 60-minute throttling (3600000ms)
- Content service: Uses `getLastContentRefreshTime()`
- Penalty service: Uses `isCacheExpired()` with `CACHE_DURATION`
- What's new service: Lightweight check, no throttling needed

## User Experience

### For Users with Cached Content
1. Loading state shows briefly
2. If error occurs, banner shows but can be dismissed
3. App remains functional with cached content
4. Auto-dismissal after 10 seconds

### For Users without Cached Content  
1. Loading state shows during fetch
2. If error occurs, banner persists until retry
3. Retry button triggers fresh API calls
4. App functionality restored after successful fetch

## Error Handling
- Network errors gracefully handled
- Cached content used as fallback
- Clear user feedback through toast states
- Automatic retry mechanism available
- Sentry error tracking maintained

## Replaced Functionality
- NoInternetScreen.tsx is no longer needed
- All network error scenarios handled by ToastBanner
- Consistent error handling across the app
- Better user experience with contextual feedback
